#!/usr/bin/env python3
"""
Script to split CSV files in the mask directory into train and test sets with a 7:3 ratio.
Randomly splits each CSV file and saves them in the respective train and test directories.
"""

import pandas as pd
import os
import glob
from sklearn.model_selection import train_test_split
import random

def split_csv_files():
    # Set random seed for reproducibility
    random.seed(42)
    
    # Define paths
    mask_dir = "/data_x/junkim100/projects/scheming_sae/itas/data/mask"
    train_dir = os.path.join(mask_dir, "train")
    test_dir = os.path.join(mask_dir, "test")
    
    # Get all CSV files in the mask directory (excluding subdirectories)
    csv_files = glob.glob(os.path.join(mask_dir, "*.csv"))
    
    print(f"Found {len(csv_files)} CSV files to split:")
    for file in csv_files:
        print(f"  - {os.path.basename(file)}")
    
    # Process each CSV file
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        print(f"\nProcessing {filename}...")
        
        try:
            # Read the CSV file
            df = pd.read_csv(csv_file)
            print(f"  Total rows: {len(df)}")
            
            # Split into train (70%) and test (30%)
            train_df, test_df = train_test_split(
                df, 
                test_size=0.3, 
                random_state=42, 
                shuffle=True
            )
            
            print(f"  Train rows: {len(train_df)}")
            print(f"  Test rows: {len(test_df)}")
            
            # Save train and test files
            train_file = os.path.join(train_dir, filename)
            test_file = os.path.join(test_dir, filename)
            
            train_df.to_csv(train_file, index=False)
            test_df.to_csv(test_file, index=False)
            
            print(f"  Saved: {train_file}")
            print(f"  Saved: {test_file}")
            
        except Exception as e:
            print(f"  Error processing {filename}: {str(e)}")
    
    print("\nSplit complete!")

if __name__ == "__main__":
    split_csv_files()
